import React from "react";
import { HelpCircle, AudioWaveformIcon as Waveform } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface ChatInputProps {
  inputValue: string;
  onInputChange: (value: string) => void;
  onSubmit: (e: React.FormEvent) => void;
  onGetHint: () => void;
  isDisabled: boolean;
  isButtonDisabled: boolean;
}

export const ChatInput: React.FC<ChatInputProps> = ({
  inputValue,
  onInputChange,
  onSubmit,
  onGetHint,
  isDisabled,
  isButtonDisabled,
}) => {
  return (
    <div className="p-3 bg-[#343541] border-t border-gray-700">
      <div className="flex gap-2 mb-3">
        <Button
          onClick={onGetHint}
          variant="outline"
          size="sm"
          className="bg-yellow-600/20 border-yellow-600/50 text-yellow-400 hover:bg-yellow-600/30"
        >
          <HelpCircle className="w-4 h-4 mr-1" />
          Hint
        </Button>
      </div>
      
      <form onSubmit={onSubmit} className="relative">
        <input
          type="text"
          value={inputValue}
          onChange={(e) => onInputChange(e.target.value)}
          disabled={isDisabled}
          placeholder="Type your answer..."
          className={cn(
            "w-full py-3 px-4 pr-16 rounded-lg text-white placeholder-gray-400 outline-none",
            isDisabled
              ? "bg-[#2a2b36] cursor-not-allowed"
              : "bg-[#40414f]"
          )}
        />
        <div className="absolute bottom-0 right-0 flex items-center h-full px-3 space-x-2 text-gray-400">
          <button
            type="submit"
            className={cn(
              "p-1 rounded",
              isButtonDisabled
                ? "bg-gray-700 text-gray-500 cursor-not-allowed"
                : "bg-white/10 hover:bg-gray-600 text-white cursor-pointer"
            )}
            disabled={isButtonDisabled}
          >
            <Waveform size={18} />
          </button>
        </div>
      </form>
    </div>
  );
};
