"use client";

import React, { useEffect, useRef, useState } from "react";
import { useGameQuestions } from "../first-game/first-game.context";

// Components
import { LoadingScreen } from "./chat-gpt/LoadingScreen";
import { FinalScoreScreen } from "./chat-gpt/FinalScoreScreen";
import { ChatHeader } from "./chat-gpt/ChatHeader";
import { ProgressIndicator } from "./chat-gpt/ProgressIndicator";
import { ChatMessageComponent } from "./chat-gpt/ChatMessage";
import { ChatInput } from "./chat-gpt/ChatInput";

// Types
import {
  ChatMessage,
  QuestionMessageContent,
  UserAnswerContent,
  ResultMessageContent,
  HintMessageContent,
  FinalScoreContent,
} from "./chat-gpt/types";

export default function ChatGPTFrame() {
  const [inputValue, setInputValue] = useState("");
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const contentRef = useRef<HTMLDivElement>(null);

  const {
    gameQuestions,
    currentQuestionIndex,
    questionResults,
    isLoadingQuestions,
    isSubmittingQuestionAnswer,
    loadQuestions,
    submitQuestionAnswer,
    getHint,
    nextQuestion,
    isGameCompleted,
    finalScore,
  } = useGameQuestions();

  // Load questions on mount
  useEffect(() => {
    if (!gameQuestions && !isLoadingQuestions) {
      loadQuestions();
    }
  }, [gameQuestions, isLoadingQuestions, loadQuestions]);

  // Initialize first question when questions are loaded
  useEffect(() => {
    if (gameQuestions && messages.length === 0) {
      const firstQuestion = gameQuestions.questions[0];
      if (firstQuestion) {
        const questionMessage: ChatMessage = {
          id: `question-${firstQuestion.id}`,
          type: "question",
          content: {
            questionId: firstQuestion.id,
            questionNumber: 1,
            question: firstQuestion.question,
            maxPoints: firstQuestion.maxPoints,
            expectedAnswersCount: firstQuestion.expectedAnswersCount,
            totalQuestions: gameQuestions.totalQuestions,
          } as QuestionMessageContent,
          timestamp: new Date(),
        };
        setMessages([questionMessage]);
      }
    }
  }, [gameQuestions, messages.length]);

  // Add new question when moving to next question
  useEffect(() => {
    if (gameQuestions && currentQuestionIndex > 0) {
      const currentQuestion = gameQuestions.questions[currentQuestionIndex];
      if (currentQuestion) {
        const questionMessage: ChatMessage = {
          id: `question-${currentQuestion.id}`,
          type: "question",
          content: {
            questionId: currentQuestion.id,
            questionNumber: currentQuestionIndex + 1,
            question: currentQuestion.question,
            maxPoints: currentQuestion.maxPoints,
            expectedAnswersCount: currentQuestion.expectedAnswersCount,
            totalQuestions: gameQuestions.totalQuestions,
          } as QuestionMessageContent,
          timestamp: new Date(),
        };
        setMessages((prev) => [...prev, questionMessage]);
      }
    }
  }, [currentQuestionIndex, gameQuestions]);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    if (contentRef.current) {
      contentRef.current.scrollTop = contentRef.current.scrollHeight;
    }
  }, [messages]);

  const currentQuestion = gameQuestions?.questions[currentQuestionIndex];
  const currentResult = questionResults[currentQuestionIndex];
  const isInputDisabled = isSubmittingQuestionAnswer;
  const isButtonDisabled = !inputValue.trim() || isInputDisabled;
  const hasAnsweredCurrentQuestion = !!currentResult;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isButtonDisabled || !currentQuestion) return;

    const userAnswer = inputValue.trim();
    setInputValue("");

    // Add user answer message
    const userAnswerMessage: ChatMessage = {
      id: `user-answer-${currentQuestion.id}-${Date.now()}`,
      type: "user-answer",
      content: {
        questionId: currentQuestion.id,
        answer: userAnswer,
      } as UserAnswerContent,
      timestamp: new Date(),
    };

    // Add loading message
    const loadingMessage: ChatMessage = {
      id: `loading-${currentQuestion.id}-${Date.now()}`,
      type: "loading",
      content: {},
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userAnswerMessage, loadingMessage]);

    try {
      const result = await submitQuestionAnswer(currentQuestion.id, userAnswer);

      // Remove loading message and add result
      setMessages((prev) => {
        const withoutLoading = prev.filter(
          (msg) => msg.id !== loadingMessage.id
        );
        const resultMessage: ChatMessage = {
          id: `result-${currentQuestion.id}-${Date.now()}`,
          type: "result",
          content: {
            questionId: result.questionId,
            question: result.question,
            totalPoints: result.totalPoints,
            maxPoints: result.maxPoints,
            correctAnswers: result.correctAnswers,
            expectedAnswersCount: result.expectedAnswersCount,
            isLastQuestion:
              currentQuestionIndex >= (gameQuestions?.totalQuestions || 0) - 1,
          } as ResultMessageContent,
          timestamp: new Date(),
        };
        return [...withoutLoading, resultMessage];
      });
    } catch (error) {
      console.error("Error submitting answer:", error);
      // Remove loading message on error
      setMessages((prev) => prev.filter((msg) => msg.id !== loadingMessage.id));
    }
  };

  const handleGetHint = async () => {
    if (!currentQuestion) return;

    try {
      const hint = await getHint(currentQuestion.id);
      const hintMessage: ChatMessage = {
        id: `hint-${currentQuestion.id}-${Date.now()}`,
        type: "hint",
        content: {
          questionId: currentQuestion.id,
          hint,
          points: 5, // Default points, could be dynamic
        } as HintMessageContent,
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, hintMessage]);
    } catch (error) {
      console.error("Error getting hint:", error);
    }
  };

  const handleNextQuestion = () => {
    nextQuestion();
  };

  if (isLoadingQuestions) {
    return <LoadingScreen />;
  }

  if (isGameCompleted) {
    const finalScoreContent: FinalScoreContent = {
      finalScore,
      totalMaxPoints: gameQuestions?.totalMaxPoints || 0,
      questionResults: questionResults.map((result) => ({
        ...result,
        isLastQuestion: false, // Not needed for final score
      })),
    };
    return <FinalScoreScreen content={finalScoreContent} />;
  }

  return (
    <div className="flex flex-col overflow-hidden border border-gray-700 shadow-xl h-full bg-[#343541]">
      <ChatHeader />

      <div ref={contentRef} className="flex-1 overflow-y-auto p-4 text-white">
        {/* Progress indicator */}
        {gameQuestions && (
          <ProgressIndicator
            currentQuestion={currentQuestionIndex + 1}
            totalQuestions={gameQuestions.totalQuestions}
          />
        )}

        {/* Chat messages */}
        {messages.map((message) => (
          <ChatMessageComponent
            key={message.id}
            message={message}
            onNextQuestion={handleNextQuestion}
          />
        ))}
      </div>

      {/* Input area - only show if current question hasn't been answered */}
      {currentQuestion && !hasAnsweredCurrentQuestion && (
        <ChatInput
          inputValue={inputValue}
          onInputChange={setInputValue}
          onSubmit={handleSubmit}
          onGetHint={handleGetHint}
          isDisabled={isInputDisabled}
          isButtonDisabled={isButtonDisabled}
        />
      )}
    </div>
  );
}
